﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>RoboMentors</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RoboMentors.Application.Contracts\RoboMentors.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi.Client" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi.Client" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi.Client" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi.Client" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Account.HttpApi.Client" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="**\*generate-proxy.json" />
    <Content Remove="**\*generate-proxy.json" />
  </ItemGroup>

</Project>
