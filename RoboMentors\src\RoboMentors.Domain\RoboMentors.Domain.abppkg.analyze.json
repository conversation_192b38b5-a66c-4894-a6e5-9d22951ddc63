{"name": "RoboMentors.Domain", "hash": "", "contents": [{"namespace": "RoboMentors", "dependsOnModules": [{"declaringAssemblyName": "RoboMentors.Domain.Shared", "namespace": "RoboMentors", "name": "RoboMentorsDomainSharedModule"}, {"declaringAssemblyName": "Volo.Abp.AuditLogging.Domain", "namespace": "Volo.Abp.AuditLogging", "name": "AbpAuditLoggingDomainModule"}, {"declaringAssemblyName": "Volo.Abp.Caching", "namespace": "Volo.Abp.Caching", "name": "AbpCachingModule"}, {"declaringAssemblyName": "Volo.Abp.BackgroundJobs.Domain", "namespace": "Volo.Abp.BackgroundJobs", "name": "AbpBackgroundJobsDomainModule"}, {"declaringAssemblyName": "Volo.Abp.FeatureManagement.Domain", "namespace": "Volo.Abp.FeatureManagement", "name": "AbpFeatureManagementDomainModule"}, {"declaringAssemblyName": "Volo.Abp.PermissionManagement.Domain.Identity", "namespace": "Volo.Abp.PermissionManagement.Identity", "name": "AbpPermissionManagementDomainIdentityModule"}, {"declaringAssemblyName": "Volo.Abp.PermissionManagement.Domain.OpenIddict", "namespace": "Volo.Abp.PermissionManagement.OpenIddict", "name": "AbpPermissionManagementDomainOpenIddictModule"}, {"declaringAssemblyName": "Volo.Abp.SettingManagement.Domain", "namespace": "Volo.Abp.SettingManagement", "name": "AbpSettingManagementDomainModule"}, {"declaringAssemblyName": "Volo.Abp.Emailing", "namespace": "Volo.Abp.Emailing", "name": "AbpEmailingModule"}, {"declaringAssemblyName": "Volo.Abp.Identity.Pro.Domain", "namespace": "Volo.Abp.Identity", "name": "AbpIdentityProDomainModule"}, {"declaringAssemblyName": "Volo.Abp.OpenIddict.Pro.Domain", "namespace": "Volo.Abp.OpenIddict", "name": "AbpOpenIddictProDomainModule"}, {"declaringAssemblyName": "Volo.Abp.TextTemplateManagement.Domain", "namespace": "Volo.Abp.TextTemplateManagement", "name": "TextTemplateManagementDomainModule"}, {"declaringAssemblyName": "Volo.Abp.LanguageManagement.Domain", "namespace": "Volo.Abp.LanguageManagement", "name": "LanguageManagementDomainModule"}, {"declaringAssemblyName": "Volo.Abp.Commercial.SuiteTemplates", "namespace": "Volo.Abp.Commercial.SuiteTemplates", "name": "VoloAbpCommercialSuiteTemplatesModule"}, {"declaringAssemblyName": "Volo.Abp.Gdpr.Domain", "namespace": "Volo.Abp.Gdpr", "name": "AbpGdprDomainModule"}, {"declaringAssemblyName": "Volo.Abp.BlobStoring.Database.Domain", "namespace": "Volo.Abp.BlobStoring.Database", "name": "BlobStoringDatabaseDomainModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "RoboMentorsDomainModule", "summary": null}]}