﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <AspNetCoreHostingModel>InProcess</AspNetCoreHostingModel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <GenerateBindingRedirectsOutputType>true</GenerateBindingRedirectsOutputType>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
    <MvcRazorExcludeRefAssembliesFromPublish>false</MvcRazorExcludeRefAssembliesFromPublish>
    <PreserveCompilationReferences>true</PreserveCompilationReferences>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MessagePack" Version="3.1.4" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.Server" Version="9.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.8" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
    <PackageReference Include="Serilog.Sinks.Async" Version="2.1.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.8.0" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.8.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="9.0.0" />
    <PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="9.0.0" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RoboMentors.Application\RoboMentors.Application.csproj" />
    <ProjectReference Include="..\RoboMentors.HttpApi\RoboMentors.HttpApi.csproj" />
    <ProjectReference Include="..\RoboMentors.EntityFrameworkCore\RoboMentors.EntityFrameworkCore.csproj" />
    <PackageReference Include="Volo.Abp.Autofac" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Swashbuckle" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.AspNetCore.Serilog" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Blazor.Server" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Blazor.Server" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Account.Web.OpenIddict" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Identity.Blazor.Server" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite" Version="4.2.3" />
    <PackageReference Include="Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme" Version="4.2.3" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Studio.Client.AspNetCore" Version="1.1.2" />
  </ItemGroup>

  <ItemGroup Condition="Exists('./openiddict.pfx')">
    <None Remove="openiddict.pfx" />
    <EmbeddedResource Include="openiddict.pfx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Pages\**\*.js">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Pages\**\*.css">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
