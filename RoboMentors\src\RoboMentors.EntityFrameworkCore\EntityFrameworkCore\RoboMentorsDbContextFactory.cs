﻿using System;
using System.IO;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;

namespace RoboMentors.EntityFrameworkCore;

/* This class is needed for EF Core console commands
 * (like Add-Migration and Update-Database commands) */
public class RoboMentorsDbContextFactory : IDesignTimeDbContextFactory<RoboMentorsDbContext>
{
    public RoboMentorsDbContext CreateDbContext(string[] args)
    {
        var configuration = BuildConfiguration();
        
        RoboMentorsEfCoreEntityExtensionMappings.Configure();

        var builder = new DbContextOptionsBuilder<RoboMentorsDbContext>()
            .UseSqlServer(configuration.GetConnectionString("Default"));
        
        return new RoboMentorsDbContext(builder.Options);
    }

    private static IConfigurationRoot BuildConfiguration()
    {
        var builder = new ConfigurationBuilder()
            .SetBasePath(Path.Combine(Directory.GetCurrentDirectory(), "../RoboMentors.DbMigrator/"))
            .AddJsonFile("appsettings.json", optional: false);

        return builder.Build();
    }
}
