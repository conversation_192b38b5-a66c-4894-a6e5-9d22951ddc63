{"name": "RoboMentors.EntityFrameworkCore", "hash": "", "contents": [{"namespace": "RoboMentors.EntityFrameworkCore", "dependsOnModules": [{"declaringAssemblyName": "RoboMentors.Domain", "namespace": "RoboMentors", "name": "RoboMentorsDomainModule"}, {"declaringAssemblyName": "Volo.Abp.PermissionManagement.EntityFrameworkCore", "namespace": "Volo.Abp.PermissionManagement.EntityFrameworkCore", "name": "AbpPermissionManagementEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.SettingManagement.EntityFrameworkCore", "namespace": "Volo.Abp.SettingManagement.EntityFrameworkCore", "name": "AbpSettingManagementEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.EntityFrameworkCore.SqlServer", "namespace": "Volo.Abp.EntityFrameworkCore.SqlServer", "name": "AbpEntityFrameworkCoreSqlServerModule"}, {"declaringAssemblyName": "Volo.Abp.BackgroundJobs.EntityFrameworkCore", "namespace": "Volo.Abp.BackgroundJobs.EntityFrameworkCore", "name": "AbpBackgroundJobsEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.AuditLogging.EntityFrameworkCore", "namespace": "Volo.Abp.AuditLogging.EntityFrameworkCore", "name": "AbpAuditLoggingEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.FeatureManagement.EntityFrameworkCore", "namespace": "Volo.Abp.FeatureManagement.EntityFrameworkCore", "name": "AbpFeatureManagementEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.Identity.Pro.EntityFrameworkCore", "namespace": "Volo.Abp.Identity.EntityFrameworkCore", "name": "AbpIdentityProEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.OpenIddict.Pro.EntityFrameworkCore", "namespace": "Volo.Abp.OpenIddict.EntityFrameworkCore", "name": "AbpOpenIddictProEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.LanguageManagement.EntityFrameworkCore", "namespace": "Volo.Abp.LanguageManagement.EntityFrameworkCore", "name": "LanguageManagementEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.TextTemplateManagement.EntityFrameworkCore", "namespace": "Volo.Abp.TextTemplateManagement.EntityFrameworkCore", "name": "TextTemplateManagementEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.Gdpr.EntityFrameworkCore", "namespace": "Volo.Abp.Gdpr", "name": "AbpGdprEntityFrameworkCoreModule"}, {"declaringAssemblyName": "Volo.Abp.BlobStoring.Database.EntityFrameworkCore", "namespace": "Volo.Abp.BlobStoring.Database.EntityFrameworkCore", "name": "BlobStoringDatabaseEntityFrameworkCoreModule"}], "implementingInterfaces": [{"name": "IAbpModule", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IAbpModule"}, {"name": "IOnPreApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPreApplicationInitialization"}, {"name": "IOnApplicationInitialization", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationInitialization"}, {"name": "IOnPostApplicationInitialization", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IOnPostApplicationInitialization"}, {"name": "IOnApplicationShutdown", "namespace": "Volo.Abp", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.IOnApplicationShutdown"}, {"name": "IPreConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPreConfigureServices"}, {"name": "IPostConfigureServices", "namespace": "Volo.Abp.Modularity", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.Modularity.IPostConfigureServices"}], "contentType": "abpModule", "name": "RoboMentorsEntityFrameworkCoreModule", "summary": null}, {"namespace": "RoboMentors.EntityFrameworkCore", "connectionStringName": "<PERSON><PERSON><PERSON>", "databaseTables": [], "replacedDbContexts": [{"name": "IIdentityDbContext", "namespace": "Volo.Abp.Identity.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.Identity.EntityFrameworkCore", "fullName": "Volo.Abp.Identity.EntityFrameworkCore.IIdentityDbContext"}], "modelBuilderExtensionMethods": [], "implementingInterfaces": [{"name": "IInfrastructure<IServiceProvider>", "namespace": "Microsoft.EntityFrameworkCore.Infrastructure", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Infrastructure.IInfrastructure<IServiceProvider>"}, {"name": "IDbContextDependencies", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbContextDependencies"}, {"name": "IDbSetCache", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbSetCache"}, {"name": "IDbContextPoolable", "namespace": "Microsoft.EntityFrameworkCore.Internal", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Internal.IDbContextPoolable"}, {"name": "IResettableService", "namespace": "Microsoft.EntityFrameworkCore.Infrastructure", "declaringAssemblyName": "Microsoft.EntityFrameworkCore", "fullName": "Microsoft.EntityFrameworkCore.Infrastructure.IResettableService"}, {"name": "IDisposable", "namespace": "System", "declaringAssemblyName": "System.Private.CoreLib", "fullName": "System.IDisposable"}, {"name": "IAsyncDisposable", "namespace": "System", "declaringAssemblyName": "System.Private.CoreLib", "fullName": "System.IAsyncDisposable"}, {"name": "IAbpEfCoreDbContext", "namespace": "Volo.Abp.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.IAbpEfCoreDbContext"}, {"name": "IEfCoreDbContext", "namespace": "Volo.Abp.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.IEfCoreDbContext"}, {"name": "IAbpEfCoreDbFunctionContext", "namespace": "Volo.Abp.EntityFrameworkCore.GlobalFilters", "declaringAssemblyName": "Volo.Abp.EntityFrameworkCore", "fullName": "Volo.Abp.EntityFrameworkCore.GlobalFilters.IAbpEfCoreDbFunctionContext"}, {"name": "ITransientDependency", "namespace": "Volo.Abp.DependencyInjection", "declaringAssemblyName": "Volo.Abp.Core", "fullName": "Volo.Abp.DependencyInjection.ITransientDependency"}, {"name": "IIdentityProDbContext", "namespace": "Volo.Abp.Identity.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.Identity.Pro.EntityFrameworkCore", "fullName": "Volo.Abp.Identity.EntityFrameworkCore.IIdentityProDbContext"}, {"name": "IIdentityDbContext", "namespace": "Volo.Abp.Identity.EntityFrameworkCore", "declaringAssemblyName": "Volo.Abp.Identity.EntityFrameworkCore", "fullName": "Volo.Abp.Identity.EntityFrameworkCore.IIdentityDbContext"}], "contentType": "efCoreDbContext", "name": "RoboMentorsDbContext", "summary": null}]}