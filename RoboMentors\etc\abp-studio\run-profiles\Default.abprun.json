﻿{
  "applications": {
    "RoboMentors.Blazor": {
      "type": "dotnet-project",
      "launchUrl": "https://localhost:44346",
      "path": "../../../src/RoboMentors.Blazor/RoboMentors.Blazor.csproj",
      "kubernetesService": ".*-blazorserver$",
      "healthCheckEndpoint": "/health-status",
      "healthUiEndpoint": "/health-ui",
      "execution": {
        "order": 2
      }
}
  },
  "containers": {
    "serviceName": "RoboMentors-Containers",
  }
}