﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>RoboMentors</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RoboMentors.Application.Contracts\RoboMentors.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.HttpApi" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.HttpApi" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.HttpApi" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Volo.Abp.Identity.HttpApi" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Account.HttpApi" Version="9.3.0" />
  </ItemGroup>

</Project>
