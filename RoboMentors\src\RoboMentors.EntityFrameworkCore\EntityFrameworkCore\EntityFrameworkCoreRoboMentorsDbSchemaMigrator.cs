﻿using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using RoboMentors.Data;
using Volo.Abp.DependencyInjection;

namespace RoboMentors.EntityFrameworkCore;

public class EntityFrameworkCoreRoboMentorsDbSchemaMigrator
    : IRoboMentorsDbSchemaMigrator, ITransientDependency
{
    private readonly IServiceProvider _serviceProvider;

    public EntityFrameworkCoreRoboMentorsDbSchemaMigrator(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task MigrateAsync()
    {
        /* We intentionally resolving the RoboMentorsDbContext
         * from IServiceProvider (instead of directly injecting it)
         * to properly get the connection string of the current tenant in the
         * current scope.
         */

        await _serviceProvider
            .GetRequiredService<RoboMentorsDbContext>()
            .Database
            .MigrateAsync();
    }
}
