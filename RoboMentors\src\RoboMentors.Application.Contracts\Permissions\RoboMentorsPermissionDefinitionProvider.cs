using RoboMentors.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;
using Volo.Abp.MultiTenancy;

namespace RoboMentors.Permissions;

public class RoboMentorsPermissionDefinitionProvider : PermissionDefinitionProvider
{
    public override void Define(IPermissionDefinitionContext context)
    {
        var myGroup = context.AddGroup(RoboMentorsPermissions.GroupName);

        //Define your own permissions here. Example:
        //myGroup.AddPermission(RoboMentorsPermissions.MyPermission1, L("Permission:MyPermission1"));
    }

    private static LocalizableString L(string name)
    {
        return LocalizableString.Create<RoboMentorsResource>(name);
    }
}
