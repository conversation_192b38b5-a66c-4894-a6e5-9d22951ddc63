﻿using Microsoft.Extensions.Localization;
using RoboMentors.Localization;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Ui.Branding;

namespace RoboMentors.Blazor;

[Dependency(ReplaceServices = true)]
public class RoboMentorsBrandingProvider : DefaultBrandingProvider
{
    private IStringLocalizer<RoboMentorsResource> _localizer;

    public RoboMentorsBrandingProvider(IStringLocalizer<RoboMentorsResource> localizer)
    {
        _localizer = localizer;
    }

    public override string AppName => _localizer["AppName"];
}
