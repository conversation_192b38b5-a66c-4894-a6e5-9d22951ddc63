{"template": "app", "imports": {"Volo.Abp.LeptonXLiteTheme": {"version": "4.2.3", "isInstalled": true}, "Volo.Abp.Account": {"version": "9.3.0", "isInstalled": true}, "Volo.Abp.OpenIddict": {"version": "9.3.0", "isInstalled": true}, "Volo.Abp.Identity": {"version": "9.3.0", "isInstalled": true}, "Volo.Abp.SettingManagement": {"version": "9.3.0", "isInstalled": true}, "Volo.Abp.PermissionManagement": {"version": "9.3.0", "isInstalled": true}, "Volo.Abp.FeatureManagement": {"version": "9.3.0", "isInstalled": true}}, "folders": {"items": {"src": {}, "test": {}}}, "packages": {"RoboMentors.Application": {"path": "src/RoboMentors.Application/RoboMentors.Application.abppkg", "folder": "src"}, "RoboMentors.Application.Tests": {"path": "test/RoboMentors.Application.Tests/RoboMentors.Application.Tests.abppkg", "folder": "test"}, "RoboMentors.Domain.Shared": {"path": "src/RoboMentors.Domain.Shared/RoboMentors.Domain.Shared.abppkg", "folder": "src"}, "RoboMentors.Application.Contracts": {"path": "src/RoboMentors.Application.Contracts/RoboMentors.Application.Contracts.abppkg", "folder": "src"}, "RoboMentors.HttpApi": {"path": "src/RoboMentors.HttpApi/RoboMentors.HttpApi.abppkg", "folder": "src"}, "RoboMentors.HttpApi.Client": {"path": "src/RoboMentors.HttpApi.Client/RoboMentors.HttpApi.Client.abppkg", "folder": "src"}, "RoboMentors.EntityFrameworkCore.Tests": {"path": "test/RoboMentors.EntityFrameworkCore.Tests/RoboMentors.EntityFrameworkCore.Tests.abppkg", "folder": "test"}, "RoboMentors.EntityFrameworkCore": {"path": "src/RoboMentors.EntityFrameworkCore/RoboMentors.EntityFrameworkCore.abppkg", "folder": "src"}, "RoboMentors.TestBase": {"path": "test/RoboMentors.TestBase/RoboMentors.TestBase.abppkg", "folder": "test"}, "RoboMentors.Domain.Tests": {"path": "test/RoboMentors.Domain.Tests/RoboMentors.Domain.Tests.abppkg", "folder": "test"}, "RoboMentors.HttpApi.Client.ConsoleTestApp": {"path": "test/RoboMentors.HttpApi.Client.ConsoleTestApp/RoboMentors.HttpApi.Client.ConsoleTestApp.abppkg", "folder": "test"}, "RoboMentors.DbMigrator": {"path": "src/RoboMentors.DbMigrator/RoboMentors.DbMigrator.abppkg", "folder": "src"}, "RoboMentors.Blazor": {"path": "src/RoboMentors.Blazor/RoboMentors.Blazor.abppkg", "folder": "src"}, "RoboMentors.Domain": {"path": "src/RoboMentors.Domain/RoboMentors.Domain.abppkg", "folder": "src"}}}